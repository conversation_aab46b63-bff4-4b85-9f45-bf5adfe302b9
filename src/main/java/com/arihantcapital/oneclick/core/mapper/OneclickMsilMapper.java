package com.arihantcapital.oneclick.core.mapper;

import com.arihantcapital.oneclick.domain.entity.Client;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class OneclickMsilMapper {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static JsonNode getMsilClientInsertRequest(Client client) {

        String dob = client.getDobOrDoi();
        // Reverse the date format from YYYY-MM-DD to DDMMYYYY
        String parsedDob = dob.replace("-", "");
        String reversedDob = parsedDob.substring(6) + parsedDob.substring(4, 6) + parsedDob.substring(0, 4);

        Map<String, Object> request = new HashMap<>();
        request.put("clientCode", client.getClientCode());
        request.put("clientName", client.getClientName());
        request.put("email", client.getEmail());
        request.put("panNumber", client.getPanNumber());
        request.put("dob", reversedDob);
        request.put("mobile", client.getMobile());

        // Convert Map to JsonNode using ObjectMapper.valueToTree()
        return objectMapper.valueToTree(request);
    }

    public static JsonNode getMsilClientInsertRequest(List<Client> clients) {
        List<JsonNode> request = new ArrayList<>();
        clients.forEach(client -> {
            request.add(getMsilClientInsertRequest(client));
        });
        return objectMapper.valueToTree(request);
    }

    public static JsonNode getMsilClientUpdateRequest(Client client, List<String> fields) {
        Map<String, Object> request = new HashMap<>();
        request.put("clientCode", client.getClientCode());
        fields.forEach(field -> {
            switch (field) {
                case "email" -> request.put("email", client.getEmail());
                case "mobile" -> request.put("mobile", client.getMobile());
                default -> log.warn("⚠️ Invalid field: {}", field);
            }
        });

        // Convert Map to JsonNode using ObjectMapper.valueToTree()
        return objectMapper.valueToTree(request);
    }

    public static JsonNode getMsilClientUpdateRequest(List<Client> clients, List<List<String>> clientFields) {
        List<JsonNode> request = new ArrayList<>();
        for (int i = 0; i < clients.size(); i++) {
            request.add(getMsilClientUpdateRequest(clients.get(i), clientFields.get(i)));
        }
        return objectMapper.valueToTree(request);
    }


    // Validation result class
    @Data
    public static class ValidationResult {
        private boolean status;
        private List<String> messages;

        public ValidationResult() {
            this.status = true;
            this.messages = new ArrayList<>();
        }

        public ValidationResult(boolean status, List<String> messages) {
            this.status = status;
            this.messages = messages != null ? messages : new ArrayList<>();
        }

        public void addError(String message) {
            this.messages.add(message);
            this.status = false;
        }

        public void addFieldError(String fieldName, String errorMessage) {
            this.addError(fieldName + ": " + errorMessage);
        }

        public boolean isValid() {
            return status && messages.isEmpty();
        }
    }

    // Helper function to validate MSIL client request data
    public static ValidationResult validateMsilClientRequest(JsonNode request) {
        ValidationResult result = new ValidationResult();

        if (request == null) {
            result.addError("Request is null");
            log.warn("⚠️ MSIL client request validation failed: Request is null");
            return result;
        }

        // Handle array of requests (bulk operations)
        if (request.isArray()) {
            if (request.size() == 0) {
                result.addError("Request array is empty");
                log.warn("⚠️ MSIL client request validation failed: Request array is empty");
                return result;
            }

            for (int i = 0; i < request.size(); i++) {
                ValidationResult singleResult = validateSingleMsilClientRequest(request.get(i));
                if (!singleResult.isValid()) {
                    // Add index prefix to error messages for bulk operations
                    for (String message : singleResult.getMessages()) {
                        result.addError("Request[" + i + "] " + message);
                    }
                }
            }

            if (result.isValid()) {
                log.info("✅ MSIL bulk client request validation successful for {} requests", request.size());
            }
            return result;
        }

        // Handle single request
        return validateSingleMsilClientRequest(request);
    }

    // Helper function to validate a single MSIL client request
    private static ValidationResult validateSingleMsilClientRequest(JsonNode request) {
        ValidationResult result = new ValidationResult();

        if (request == null || request.isNull()) {
            result.addError("Single request is null");
            log.warn("⚠️ MSIL client request validation failed: Single request is null");
            return result;
        }

        // Check required field: clientCode (always required for both insert and update)
        if (!isValidStringField(request, "clientCode")) {
            result.addFieldError("clientCode", "is missing or invalid");
        }

        // Determine if this is an insert or update request based on fields present
        boolean isInsertRequest = request.has("clientName") && request.has("panNumber") && request.has("dob");

        if (isInsertRequest) {
            ValidationResult insertResult = validateInsertRequest(request);
            result.getMessages().addAll(insertResult.getMessages());
            if (!insertResult.isValid()) {
                result.setStatus(false);
            }
        } else {
            ValidationResult updateResult = validateUpdateRequest(request);
            result.getMessages().addAll(updateResult.getMessages());
            if (!updateResult.isValid()) {
                result.setStatus(false);
            }
        }

        return result;
    }

    // Validate insert request - all fields are required
    private static ValidationResult validateInsertRequest(JsonNode request) {
        ValidationResult result = new ValidationResult();
        String[] requiredFields = {"clientCode", "clientName", "email", "panNumber", "dob", "mobile"};

        // First check if all required fields are present and not empty
        for (String field : requiredFields) {
            if (!isValidStringField(request, field)) {
                result.addFieldError(field, "is required and cannot be empty");
            }
        }

        // Additional format validation for specific fields (only if they are present and not empty)
        if (isValidStringField(request, "email")) {
            if (!isValidEmail(request.get("email").asText())) {
                result.addFieldError("email", "format is invalid");
            }
        }

        if (isValidStringField(request, "mobile")) {
            if (!isValidMobile(request.get("mobile").asText())) {
                result.addFieldError("mobile", "format is invalid (must be 10 digits)");
            }
        }

        if (isValidStringField(request, "dob")) {
            if (!isValidDob(request.get("dob").asText())) {
                result.addFieldError("dob", "format is invalid (must be DDMMYYYY)");
            }
        }

        if (result.isValid()) {
            String clientCode = request.has("clientCode") ? request.get("clientCode").asText() : "unknown";
            log.info("✅ MSIL insert request validation successful for clientCode: {}", clientCode);
        } else {
            log.warn("⚠️ MSIL insert request validation failed with {} errors", result.getMessages().size());
        }

        return result;
    }

    // Validate update request - clientCode is required, at least one updateable field must be present and valid
    private static ValidationResult validateUpdateRequest(JsonNode request) {
        ValidationResult result = new ValidationResult();

        // clientCode is always required
        if (!isValidStringField(request, "clientCode")) {
            result.addFieldError("clientCode", "is missing or invalid");
        }

        // At least one updatable field must be present and valid
        boolean hasValidUpdateField = false;

        if (request.has("email")) {
            if (!isValidStringField(request, "email")) {
                result.addFieldError("email", "is present but empty or invalid");
            } else if (!isValidEmail(request.get("email").asText())) {
                result.addFieldError("email", "format is invalid");
            } else {
                hasValidUpdateField = true;
            }
        }

        if (request.has("mobile")) {
            if (!isValidStringField(request, "mobile")) {
                result.addFieldError("mobile", "is present but empty or invalid");
            } else if (!isValidMobile(request.get("mobile").asText())) {
                result.addFieldError("mobile", "format is invalid (must be 10 digits)");
            } else {
                hasValidUpdateField = true;
            }
        }

        if (!hasValidUpdateField && !request.has("email") && !request.has("mobile")) {
            result.addError("No updateable fields (email, mobile) found");
        }

        if (result.isValid()) {
            String clientCode = request.has("clientCode") ? request.get("clientCode").asText() : "unknown";
            log.info("✅ MSIL update request validation successful for clientCode: {}", clientCode);
        } else {
            log.warn("⚠️ MSIL update request validation failed with {} errors", result.getMessages().size());
        }

        return result;
    }

    // Helper method to validate string fields for null, empty, or whitespace-only values
    private static boolean isValidStringField(JsonNode request, String fieldName) {
        if (!request.has(fieldName)) {
            return false;
        }

        JsonNode fieldNode = request.get(fieldName);
        if (fieldNode == null || fieldNode.isNull()) {
            return false;
        }

        String value = fieldNode.asText();
        return value != null && !value.trim().isEmpty();
    }

    // Helper method to validate email format
    private static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }

    // Helper method to validate mobile format (assuming 10 digits)
    private static boolean isValidMobile(String mobile) {
        if (mobile == null || mobile.trim().isEmpty()) {
            return false;
        }
        return mobile.matches("^[0-9]{10}$");
    }

    // Helper method to validate DOB format (DDMMYYYY)
    private static boolean isValidDob(String dob) {
        if (dob == null || dob.trim().isEmpty()) {
            return false;
        }
        return dob.matches("^[0-9]{8}$");
    }


    // Example usage of validation
    public static void main(String[] args) {
        // Example 1: Valid insert request
        Map<String, Object> validInsert = new HashMap<>();
        validInsert.put("clientCode", "CLI001");
        validInsert.put("clientName", "John Doe");
        validInsert.put("email", "<EMAIL>");
        validInsert.put("panNumber", "**********");
        validInsert.put("dob", "15071990");
        validInsert.put("mobile", "9876543210");

        JsonNode validInsertNode = objectMapper.valueToTree(validInsert);
        ValidationResult result1 = validateMsilClientRequest(validInsertNode);
        System.out.println("Valid Insert - Status: " + result1.isStatus() + ", Messages: " + result1.getMessages());

        // Example 2: Invalid insert request
        Map<String, Object> invalidInsert = new HashMap<>();
        invalidInsert.put("clientCode", "AP0204763");
        invalidInsert.put("clientName", "Jane Doe");
        invalidInsert.put("email", "<EMAIL>");
        invalidInsert.put("mobile", "8309666700"); // Invalid mobile

        JsonNode invalidInsertNode = objectMapper.valueToTree(invalidInsert);
        ValidationResult result2 = validateMsilClientRequest(invalidInsertNode);
        System.out.println("Invalid Insert - Status: " + result2.isStatus() + ", Messages: " + result2.getMessages());

        // Example 3: Valid update request
        Map<String, Object> validUpdate = new HashMap<>();
        validUpdate.put("clientCode", "CLI001");
        validUpdate.put("email", "<EMAIL>");

        JsonNode validUpdateNode = objectMapper.valueToTree(validUpdate);
        ValidationResult result3 = validateMsilClientRequest(validUpdateNode);
        System.out.println("Valid Update - Status: " + result3.isStatus() + ", Messages: " + result3.getMessages());
    }
}
