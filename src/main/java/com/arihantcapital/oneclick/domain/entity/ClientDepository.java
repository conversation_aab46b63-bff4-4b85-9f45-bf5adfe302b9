package com.arihantcapital.oneclick.domain.entity;

import jakarta.persistence.*;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "client_depositories")
@Getter
@Setter
public class ClientDepository {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "identifier", unique = true, nullable = false)
    private String identifier;

    @Column(name = "client_code", nullable = false)
    private String clientCode;

    @Column(name = "depository_name")
    private String depositoryName;

    @Column(name = "depository_id")
    private String depositoryId;

    @Column(name = "depository_type")
    private String depositoryType;

    @Column(name = "depository_client_id")
    private String depositoryClientId;

    @Column(name = "depository_status")
    private String depositoryStatus;

    @Column(name = "ddpi")
    private String ddpi;

    @Column(name = "poa")
    private String poa;

    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    @PrePersist
    public void prePersist() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        updatedAt = Instant.now();
    }
}
